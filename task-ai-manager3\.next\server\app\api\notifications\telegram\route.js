/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/notifications/telegram/route";
exports.ids = ["app/api/notifications/telegram/route"];
exports.modules = {

/***/ "(rsc)/./app/api/notifications/telegram/route.ts":
/*!*************************************************!*\
  !*** ./app/api/notifications/telegram/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_telegram_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/telegram-service */ \"(rsc)/./lib/telegram-service.ts\");\n\n\n// Обработчик POST-запроса для отправки Telegram-уведомлений\nasync function POST(request) {\n    try {\n        // Получаем данные из запроса\n        const data = await request.json();\n        // Проверяем наличие необходимых данных\n        if (!data.telegramUsername || !data.message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Формируем сообщение для Telegram\n        const messageText = `📬 *${data.subject || 'Уведомление'}*\\n\\n${data.message}\\n\\n*Сообщение:* ${data.messageText}\\n*От:* ${data.senderName}\\n*Чат:* ${data.chatName}\\n\\n_AI Task Tracker_`;\n        // Отправляем сообщение через Telegram Bot API\n        const result = await (0,_lib_telegram_service__WEBPACK_IMPORTED_MODULE_1__.sendTelegramMessage)({\n            chatId: data.telegramUsername,\n            message: messageText,\n            parseMode: 'Markdown'\n        });\n        if (result) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: `Telegram notification sent to @${data.telegramUsername}`\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Failed to send Telegram notification'\n            }, {\n                status: 500\n            });\n        }\n    /*\n    // Пример реальной отправки сообщения через Telegram Bot API\n    const botToken = process.env.TELEGRAM_BOT_TOKEN;\n    if (!botToken) {\n      throw new Error('TELEGRAM_BOT_TOKEN is not defined');\n    }\n\n    // Формируем текст сообщения\n    const messageText = `\n📬 *${data.subject}*\n\n${data.message}\n\n*Сообщение:* ${data.messageText}\n*От:* ${data.senderName}\n*Чат:* ${data.chatName}\n    `;\n\n    // Отправляем запрос к Telegram Bot API\n    const response = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        chat_id: data.telegramUsername,\n        text: messageText,\n        parse_mode: 'Markdown',\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(`Telegram API error: ${JSON.stringify(errorData)}`);\n    }\n\n    const responseData = await response.json();\n\n    return NextResponse.json({\n      success: true,\n      message: `Telegram notification sent to @${data.telegramUsername}`,\n      telegramResponse: responseData\n    });\n    */ } catch (error) {\n        console.error('Error sending Telegram notification:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to send Telegram notification',\n            error: String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/notifications/telegram/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/telegram-service.ts":
/*!*********************************!*\
  !*** ./lib/telegram-service.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTelegramBotInfo: () => (/* binding */ getTelegramBotInfo),\n/* harmony export */   sendTelegramMessage: () => (/* binding */ sendTelegramMessage),\n/* harmony export */   sendTelegramReminder: () => (/* binding */ sendTelegramReminder),\n/* harmony export */   validateTelegramChatId: () => (/* binding */ validateTelegramChatId)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"0004d75c0cbd3b51b50db872a1bb33175142ec7a81\":\"getTelegramBotInfo\",\"4036d186bba33a6111e3a1a311e5529f6815449ecf\":\"validateTelegramChatId\",\"40d1ad41895a6b2d78824c2a72a188ce00547f82db\":\"sendTelegramReminder\",\"40ddd58023aecf11b09e60f5964dbdfe2e36fe9c17\":\"sendTelegramMessage\"} */ \n\n// Получение настроек Telegram Bot из переменных окружения\nfunction getTelegramSettings() {\n    const botToken = process.env.TELEGRAM_BOT_TOKEN;\n    if (!botToken) {\n        throw new Error('TELEGRAM_BOT_TOKEN is not configured');\n    }\n    return {\n        botToken,\n        apiUrl: `https://api.telegram.org/bot${botToken}`\n    };\n}\n// Функция для отправки сообщения через Telegram Bot API\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ sendTelegramMessage(data) {\n    try {\n        const settings = getTelegramSettings();\n        // Проверяем, что все необходимые данные указаны\n        if (!data.chatId || !data.message) {\n            console.error('Telegram chat ID and message are required');\n            return false;\n        }\n        // Формируем тело запроса\n        const requestBody = {\n            chat_id: data.chatId,\n            text: data.message,\n            parse_mode: data.parseMode || 'Markdown',\n            disable_web_page_preview: data.disableWebPagePreview || false,\n            disable_notification: data.disableNotification || false\n        };\n        // Отправляем запрос к Telegram Bot API\n        const response = await fetch(`${settings.apiUrl}/sendMessage`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            console.error('Telegram API error:', errorData);\n            return false;\n        }\n        const responseData = await response.json();\n        console.log(`Telegram message sent to ${data.chatId}: ${responseData.result.message_id}`);\n        // Записываем информацию об отправленном сообщении в лог\n        await logTelegramMessageSent(data);\n        return true;\n    } catch (error) {\n        console.error('Error sending Telegram message:', error);\n        return false;\n    }\n}\n// Функция для отправки напоминания в Telegram\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ sendTelegramReminder(reminderData) {\n    try {\n        // Проверяем, что у пользователя есть Telegram chat ID\n        if (!reminderData.telegramChatId) {\n            console.error(`User ${reminderData.userId} does not have a Telegram chat ID`);\n            return false;\n        }\n        // Формируем сообщение для Telegram\n        const messageText = formatTelegramReminderMessage(reminderData);\n        // Отправляем сообщение\n        const result = await sendTelegramMessage({\n            chatId: reminderData.telegramChatId,\n            message: messageText,\n            parseMode: 'Markdown'\n        });\n        return result;\n    } catch (error) {\n        console.error('Error sending Telegram reminder:', error);\n        return false;\n    }\n}\n// Функция для форматирования сообщения напоминания для Telegram\nfunction formatTelegramReminderMessage(reminderData) {\n    const { userName, subject, message, itemTitle, itemType, dueDate, projectName } = reminderData;\n    let messageText = `🔔 *${subject}*\\n\\n`;\n    messageText += `Привет, ${userName}!\\n\\n`;\n    messageText += `${message}\\n\\n`;\n    // Добавляем информацию о элементе\n    const typeEmoji = getTypeEmoji(itemType);\n    messageText += `${typeEmoji} *${itemTitle}*\\n`;\n    if (projectName) {\n        messageText += `📁 Проект: ${projectName}\\n`;\n    }\n    if (dueDate) {\n        messageText += `📅 Срок: ${dueDate.toLocaleDateString('ru-RU')}\\n`;\n    }\n    messageText += `\\n_AI Task Tracker_`;\n    return messageText;\n}\n// Функция для получения эмодзи по типу элемента\nfunction getTypeEmoji(itemType) {\n    switch(itemType){\n        case 'task':\n            return '✅';\n        case 'subtask':\n            return '📝';\n        case 'project':\n            return '📁';\n        case 'event':\n            return '📅';\n        default:\n            return '📌';\n    }\n}\n// Функция для записи информации об отправленном сообщении в лог\nasync function logTelegramMessageSent(data) {\n    try {\n        // В реальном приложении здесь будет запись в базу данных\n        console.log(`Telegram message sent to ${data.chatId}: ${data.message.substring(0, 50)}...`);\n    // Пример записи в базу данных (закомментировано, так как модель не существует)\n    /*\n    await prisma.telegramLog.create({\n      data: {\n        chatId: data.chatId,\n        message: data.message,\n        sentAt: new Date(),\n        success: true,\n      },\n    });\n    */ } catch (error) {\n        console.error('Error logging Telegram message:', error);\n    }\n}\n// Функция для получения информации о боте\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ getTelegramBotInfo() {\n    try {\n        const settings = getTelegramSettings();\n        const response = await fetch(`${settings.apiUrl}/getMe`);\n        if (!response.ok) {\n            throw new Error(`Failed to get bot info: ${response.statusText}`);\n        }\n        const data = await response.json();\n        return data.result;\n    } catch (error) {\n        console.error('Error getting Telegram bot info:', error);\n        return null;\n    }\n}\n// Функция для проверки валидности Telegram chat ID\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ validateTelegramChatId(chatId) {\n    try {\n        const result = await sendTelegramMessage({\n            chatId,\n            message: '🤖 Тест соединения с AI Task Tracker успешен!',\n            disableNotification: true\n        });\n        return result;\n    } catch (error) {\n        console.error('Error validating Telegram chat ID:', error);\n        return false;\n    }\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_2__.ensureServerEntryExports)([\n    sendTelegramMessage,\n    sendTelegramReminder,\n    getTelegramBotInfo,\n    validateTelegramChatId\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(sendTelegramMessage, \"40ddd58023aecf11b09e60f5964dbdfe2e36fe9c17\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(sendTelegramReminder, \"40d1ad41895a6b2d78824c2a72a188ce00547f82db\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(getTelegramBotInfo, \"0004d75c0cbd3b51b50db872a1bb33175142ec7a81\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(validateTelegramChatId, \"4036d186bba33a6111e3a1a311e5529f6815449ecf\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/telegram-service.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftelegram%2Froute&page=%2Fapi%2Fnotifications%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftelegram%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftelegram%2Froute&page=%2Fapi%2Fnotifications%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftelegram%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Projects_task_ai_manager3_app_api_notifications_telegram_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/notifications/telegram/route.ts */ \"(rsc)/./app/api/notifications/telegram/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/notifications/telegram/route\",\n        pathname: \"/api/notifications/telegram\",\n        filename: \"route\",\n        bundlePath: \"app/api/notifications/telegram/route\"\n    },\n    resolvedPagePath: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\api\\\\notifications\\\\telegram\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Projects_task_ai_manager3_app_api_notifications_telegram_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftelegram%2Froute&page=%2Fapi%2Fnotifications%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftelegram%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctelegram-service.ts%22%2C%5B%7B%22id%22%3A%220004d75c0cbd3b51b50db872a1bb33175142ec7a81%22%2C%22exportedName%22%3A%22getTelegramBotInfo%22%7D%2C%7B%22id%22%3A%224036d186bba33a6111e3a1a311e5529f6815449ecf%22%2C%22exportedName%22%3A%22validateTelegramChatId%22%7D%2C%7B%22id%22%3A%2240d1ad41895a6b2d78824c2a72a188ce00547f82db%22%2C%22exportedName%22%3A%22sendTelegramReminder%22%7D%2C%7B%22id%22%3A%2240ddd58023aecf11b09e60f5964dbdfe2e36fe9c17%22%2C%22exportedName%22%3A%22sendTelegramMessage%22%7D%5D%5D%5D&__client_imported__=!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctelegram-service.ts%22%2C%5B%7B%22id%22%3A%220004d75c0cbd3b51b50db872a1bb33175142ec7a81%22%2C%22exportedName%22%3A%22getTelegramBotInfo%22%7D%2C%7B%22id%22%3A%224036d186bba33a6111e3a1a311e5529f6815449ecf%22%2C%22exportedName%22%3A%22validateTelegramChatId%22%7D%2C%7B%22id%22%3A%2240d1ad41895a6b2d78824c2a72a188ce00547f82db%22%2C%22exportedName%22%3A%22sendTelegramReminder%22%7D%2C%7B%22id%22%3A%2240ddd58023aecf11b09e60f5964dbdfe2e36fe9c17%22%2C%22exportedName%22%3A%22sendTelegramMessage%22%7D%5D%5D%5D&__client_imported__=! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"0004d75c0cbd3b51b50db872a1bb33175142ec7a81\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_0__.getTelegramBotInfo),\n/* harmony export */   \"4036d186bba33a6111e3a1a311e5529f6815449ecf\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_0__.validateTelegramChatId),\n/* harmony export */   \"40d1ad41895a6b2d78824c2a72a188ce00547f82db\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_0__.sendTelegramReminder),\n/* harmony export */   \"40ddd58023aecf11b09e60f5964dbdfe2e36fe9c17\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_0__.sendTelegramMessage)\n/* harmony export */ });\n/* harmony import */ var D_Projects_task_ai_manager3_lib_telegram_service_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/telegram-service.ts */ \"(rsc)/./lib/telegram-service.ts\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyRCUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDdGFzay1haS1tYW5hZ2VyMyU1QyU1Q2xpYiU1QyU1Q3RlbGVncmFtLXNlcnZpY2UudHMlMjIlMkMlNUIlN0IlMjJpZCUyMiUzQSUyMjAwMDRkNzVjMGNiZDNiNTFiNTBkYjg3MmExYmIzMzE3NTE0MmVjN2E4MSUyMiUyQyUyMmV4cG9ydGVkTmFtZSUyMiUzQSUyMmdldFRlbGVncmFtQm90SW5mbyUyMiU3RCUyQyU3QiUyMmlkJTIyJTNBJTIyNDAzNmQxODZiYmEzM2E2MTExZTNhMWEzMTFlNTUyOWY2ODE1NDQ5ZWNmJTIyJTJDJTIyZXhwb3J0ZWROYW1lJTIyJTNBJTIydmFsaWRhdGVUZWxlZ3JhbUNoYXRJZCUyMiU3RCUyQyU3QiUyMmlkJTIyJTNBJTIyNDBkMWFkNDE4OTVhNmIyZDc4ODI0YzJhNzJhMTg4Y2UwMDU0N2Y4MmRiJTIyJTJDJTIyZXhwb3J0ZWROYW1lJTIyJTNBJTIyc2VuZFRlbGVncmFtUmVtaW5kZXIlMjIlN0QlMkMlN0IlMjJpZCUyMiUzQSUyMjQwZGRkNTgwMjNhZWNmMTFiMDllNjBmNTk2NGRiZGZlMmUzNmZlOWMxNyUyMiUyQyUyMmV4cG9ydGVkTmFtZSUyMiUzQSUyMnNlbmRUZWxlZ3JhbU1lc3NhZ2UlMjIlN0QlNUQlNUQlNUQmX19jbGllbnRfaW1wb3J0ZWRfXz0hIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUM2STtBQUNJO0FBQ0Y7QUFDRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZ2V0VGVsZWdyYW1Cb3RJbmZvIGFzIFwiMDAwNGQ3NWMwY2JkM2I1MWI1MGRiODcyYTFiYjMzMTc1MTQyZWM3YTgxXCIgfSBmcm9tIFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxsaWJcXFxcdGVsZWdyYW0tc2VydmljZS50c1wiXG5leHBvcnQgeyB2YWxpZGF0ZVRlbGVncmFtQ2hhdElkIGFzIFwiNDAzNmQxODZiYmEzM2E2MTExZTNhMWEzMTFlNTUyOWY2ODE1NDQ5ZWNmXCIgfSBmcm9tIFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxsaWJcXFxcdGVsZWdyYW0tc2VydmljZS50c1wiXG5leHBvcnQgeyBzZW5kVGVsZWdyYW1SZW1pbmRlciBhcyBcIjQwZDFhZDQxODk1YTZiMmQ3ODgyNGMyYTcyYTE4OGNlMDA1NDdmODJkYlwiIH0gZnJvbSBcIkQ6XFxcXFByb2plY3RzXFxcXHRhc2stYWktbWFuYWdlcjNcXFxcbGliXFxcXHRlbGVncmFtLXNlcnZpY2UudHNcIlxuZXhwb3J0IHsgc2VuZFRlbGVncmFtTWVzc2FnZSBhcyBcIjQwZGRkNTgwMjNhZWNmMTFiMDllNjBmNTk2NGRiZGZlMmUzNmZlOWMxN1wiIH0gZnJvbSBcIkQ6XFxcXFByb2plY3RzXFxcXHRhc2stYWktbWFuYWdlcjNcXFxcbGliXFxcXHRlbGVncmFtLXNlcnZpY2UudHNcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Ctelegram-service.ts%22%2C%5B%7B%22id%22%3A%220004d75c0cbd3b51b50db872a1bb33175142ec7a81%22%2C%22exportedName%22%3A%22getTelegramBotInfo%22%7D%2C%7B%22id%22%3A%224036d186bba33a6111e3a1a311e5529f6815449ecf%22%2C%22exportedName%22%3A%22validateTelegramChatId%22%7D%2C%7B%22id%22%3A%2240d1ad41895a6b2d78824c2a72a188ce00547f82db%22%2C%22exportedName%22%3A%22sendTelegramReminder%22%7D%2C%7B%22id%22%3A%2240ddd58023aecf11b09e60f5964dbdfe2e36fe9c17%22%2C%22exportedName%22%3A%22sendTelegramMessage%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Ftelegram%2Froute&page=%2Fapi%2Fnotifications%2Ftelegram%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Ftelegram%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();