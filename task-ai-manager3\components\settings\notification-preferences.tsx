"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"
import { Badge } from "@/components/ui/badge"
import { 
  Mail, 
  MessageCircle, 
  Clock, 
  Bell, 
  BellOff, 
  Plus, 
  X, 
  TestTube,
  CheckCircle,
  AlertCircle,
  Moon
} from "lucide-react"
import { useTranslation } from "@/lib/translations"
import { 
  getUserReminderPreferences, 
  saveUserReminderPreferences, 
  UserReminderPreferences 
} from "@/lib/enhanced-reminder-service"
import { validateTelegramChatId, sendTelegramMessage } from "@/lib/telegram-service"

interface NotificationPreferencesProps {
  userId: string
  userEmail?: string
}

export function NotificationPreferences({ userId, userEmail }: NotificationPreferencesProps) {
  const { t } = useTranslation()
  const { toast } = useToast()
  
  // State for preferences
  const [preferences, setPreferences] = useState<UserReminderPreferences>(() => 
    getUserReminderPreferences(userId)
  )
  
  // UI state
  const [isTestingTelegram, setIsTestingTelegram] = useState(false)
  const [telegramTestResult, setTelegramTestResult] = useState<'success' | 'error' | null>(null)
  const [newReminderTime, setNewReminderTime] = useState('')
  const [newReminderUnit, setNewReminderUnit] = useState<'minutes' | 'hours' | 'days'>('minutes')
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  
  // Load preferences on component mount
  useEffect(() => {
    const loadedPreferences = getUserReminderPreferences(userId)
    setPreferences(loadedPreferences)
    
    // Set default email if not set
    if (!loadedPreferences.emailForReminders && userEmail) {
      setPreferences(prev => ({ ...prev, emailForReminders: userEmail }))
      setHasUnsavedChanges(true)
    }
  }, [userId, userEmail])
  
  // Update preferences and mark as changed
  const updatePreferences = (updates: Partial<UserReminderPreferences>) => {
    setPreferences(prev => ({ ...prev, ...updates }))
    setHasUnsavedChanges(true)
  }
  
  // Save preferences
  const handleSavePreferences = async () => {
    try {
      saveUserReminderPreferences(preferences)
      setHasUnsavedChanges(false)
      
      toast({
        title: "Settings Saved",
        description: "Your notification preferences have been updated successfully.",
      })
    } catch (error) {
      console.error('Error saving preferences:', error)
      toast({
        title: "Error",
        description: "Failed to save notification preferences. Please try again.",
        variant: "destructive"
      })
    }
  }
  
  // Test Telegram connection
  const handleTestTelegram = async () => {
    if (!preferences.telegramChatId.trim()) {
      toast({
        title: "Missing Chat ID",
        description: "Please enter your Telegram chat ID first.",
        variant: "destructive"
      })
      return
    }
    
    setIsTestingTelegram(true)
    setTelegramTestResult(null)
    
    try {
      // First validate the chat ID format
      const isValidFormat = await validateTelegramChatId(preferences.telegramChatId)
      if (!isValidFormat) {
        throw new Error('Invalid Telegram chat ID format')
      }
      
      // Send test message
      const testMessage = `🤖 Test notification from AI Task Tracker\n\nIf you received this message, your Telegram notifications are working correctly!\n\nTime: ${new Date().toLocaleString()}`
      
      const success = await sendTelegramMessage({
        chatId: preferences.telegramChatId,
        message: testMessage,
        parseMode: 'Markdown'
      })
      
      if (success) {
        setTelegramTestResult('success')
        toast({
          title: "Test Successful",
          description: "Test message sent to Telegram successfully!",
        })
      } else {
        throw new Error('Failed to send test message')
      }
    } catch (error) {
      console.error('Telegram test failed:', error)
      setTelegramTestResult('error')
      toast({
        title: "Test Failed",
        description: "Failed to send test message. Please check your chat ID and try again.",
        variant: "destructive"
      })
    } finally {
      setIsTestingTelegram(false)
    }
  }
  
  // Add new reminder time
  const handleAddReminderTime = () => {
    const timeValue = parseInt(newReminderTime)
    if (isNaN(timeValue) || timeValue <= 0) {
      toast({
        title: "Invalid Time",
        description: "Please enter a valid positive number.",
        variant: "destructive"
      })
      return
    }
    
    // Convert to minutes
    let minutes = timeValue
    if (newReminderUnit === 'hours') {
      minutes = timeValue * 60
    } else if (newReminderUnit === 'days') {
      minutes = timeValue * 60 * 24
    }
    
    // Check if already exists
    if (preferences.reminderTimes.includes(minutes)) {
      toast({
        title: "Duplicate Time",
        description: "This reminder time already exists.",
        variant: "destructive"
      })
      return
    }
    
    // Add and sort
    const newTimes = [...preferences.reminderTimes, minutes].sort((a, b) => b - a)
    updatePreferences({ reminderTimes: newTimes })
    
    // Reset form
    setNewReminderTime('')
    setNewReminderUnit('minutes')
  }
  
  // Remove reminder time
  const handleRemoveReminderTime = (minutes: number) => {
    const newTimes = preferences.reminderTimes.filter(time => time !== minutes)
    updatePreferences({ reminderTimes: newTimes })
  }
  
  // Format reminder time for display
  const formatReminderTime = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      if (remainingMinutes === 0) {
        return `${hours} hour${hours !== 1 ? 's' : ''}`
      } else {
        return `${hours}h ${remainingMinutes}m`
      }
    } else {
      const days = Math.floor(minutes / 1440)
      const remainingHours = Math.floor((minutes % 1440) / 60)
      if (remainingHours === 0) {
        return `${days} day${days !== 1 ? 's' : ''}`
      } else {
        return `${days}d ${remainingHours}h`
      }
    }
  }
  
  return (
    <div className="space-y-6">
      {/* Email Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Notifications
          </CardTitle>
          <CardDescription>
            Configure email reminder settings and preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Enable Email Reminders</Label>
              <p className="text-sm text-muted-foreground">
                Receive task and project reminders via email
              </p>
            </div>
            <Switch
              checked={preferences.emailReminders}
              onCheckedChange={(checked) => updatePreferences({ emailReminders: checked })}
            />
          </div>
          
          {preferences.emailReminders && (
            <div className="space-y-2">
              <Label htmlFor="email">Email Address for Reminders</Label>
              <Input
                id="email"
                type="email"
                value={preferences.emailForReminders}
                onChange={(e) => updatePreferences({ emailForReminders: e.target.value })}
                placeholder="<EMAIL>"
              />
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Telegram Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Telegram Notifications
          </CardTitle>
          <CardDescription>
            Set up Telegram bot integration for instant notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Enable Telegram Reminders</Label>
              <p className="text-sm text-muted-foreground">
                Receive notifications through Telegram bot
              </p>
            </div>
            <Switch
              checked={preferences.telegramReminders}
              onCheckedChange={(checked) => updatePreferences({ telegramReminders: checked })}
            />
          </div>
          
          {preferences.telegramReminders && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="telegram-chat-id">Telegram Chat ID</Label>
                <div className="flex gap-2">
                  <Input
                    id="telegram-chat-id"
                    value={preferences.telegramChatId}
                    onChange={(e) => updatePreferences({ telegramChatId: e.target.value })}
                    placeholder="123456789 or @username"
                  />
                  <Button
                    variant="outline"
                    onClick={handleTestTelegram}
                    disabled={isTestingTelegram || !preferences.telegramChatId.trim()}
                  >
                    {isTestingTelegram ? (
                      <TestTube className="h-4 w-4 animate-spin" />
                    ) : (
                      <TestTube className="h-4 w-4" />
                    )}
                    Test
                  </Button>
                </div>
                {telegramTestResult && (
                  <div className={`flex items-center gap-2 text-sm ${
                    telegramTestResult === 'success' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {telegramTestResult === 'success' ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <AlertCircle className="h-4 w-4" />
                    )}
                    {telegramTestResult === 'success' 
                      ? 'Test message sent successfully!' 
                      : 'Failed to send test message. Check your chat ID.'}
                  </div>
                )}
              </div>
              
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  <strong>How to get your Chat ID:</strong><br />
                  1. Start a chat with @userinfobot on Telegram<br />
                  2. Send any message to get your Chat ID<br />
                  3. Copy the number and paste it above
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Reminder Timing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Reminder Timing
          </CardTitle>
          <CardDescription>
            Configure when you want to receive reminders before deadlines
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Current Reminder Times</Label>
            <div className="flex flex-wrap gap-2">
              {preferences.reminderTimes.map((minutes) => (
                <Badge key={minutes} variant="secondary" className="flex items-center gap-1">
                  {formatReminderTime(minutes)} before
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                    onClick={() => handleRemoveReminderTime(minutes)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
              {preferences.reminderTimes.length === 0 && (
                <p className="text-sm text-muted-foreground">No reminder times configured</p>
              )}
            </div>
          </div>
          
          <div className="flex gap-2">
            <Input
              type="number"
              placeholder="15"
              value={newReminderTime}
              onChange={(e) => setNewReminderTime(e.target.value)}
              className="w-20"
            />
            <Select value={newReminderUnit} onValueChange={(value: any) => setNewReminderUnit(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="minutes">Minutes</SelectItem>
                <SelectItem value="hours">Hours</SelectItem>
                <SelectItem value="days">Days</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={handleAddReminderTime}
              disabled={!newReminderTime.trim()}
            >
              <Plus className="h-4 w-4" />
              Add
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {/* Notification Types */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Types
          </CardTitle>
          <CardDescription>
            Choose which types of items you want to receive reminders for
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <Label>Task Reminders</Label>
              <Switch
                checked={preferences.taskReminders}
                onCheckedChange={(checked) => updatePreferences({ taskReminders: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label>Project Reminders</Label>
              <Switch
                checked={preferences.projectReminders}
                onCheckedChange={(checked) => updatePreferences({ projectReminders: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label>Calendar Reminders</Label>
              <Switch
                checked={preferences.calendarReminders}
                onCheckedChange={(checked) => updatePreferences({ calendarReminders: checked })}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label>Overdue Notifications</Label>
              <Switch
                checked={preferences.overdueReminders}
                onCheckedChange={(checked) => updatePreferences({ overdueReminders: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Quiet Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Moon className="h-5 w-5" />
            Quiet Hours
          </CardTitle>
          <CardDescription>
            Set times when you don't want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Enable Quiet Hours</Label>
              <p className="text-sm text-muted-foreground">
                Notifications will be delayed until quiet hours end
              </p>
            </div>
            <Switch
              checked={preferences.quietHours.enabled}
              onCheckedChange={(checked) => 
                updatePreferences({ 
                  quietHours: { ...preferences.quietHours, enabled: checked }
                })
              }
            />
          </div>
          
          {preferences.quietHours.enabled && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quiet-start">Start Time</Label>
                <Input
                  id="quiet-start"
                  type="time"
                  value={preferences.quietHours.start}
                  onChange={(e) => 
                    updatePreferences({ 
                      quietHours: { ...preferences.quietHours, start: e.target.value }
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="quiet-end">End Time</Label>
                <Input
                  id="quiet-end"
                  type="time"
                  value={preferences.quietHours.end}
                  onChange={(e) => 
                    updatePreferences({ 
                      quietHours: { ...preferences.quietHours, end: e.target.value }
                    })
                  }
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Save Button */}
      <div className="flex justify-end">
        <Button 
          onClick={handleSavePreferences}
          disabled={!hasUnsavedChanges}
          className="min-w-32"
        >
          {hasUnsavedChanges ? 'Save Changes' : 'Saved'}
        </Button>
      </div>
    </div>
  )
}
