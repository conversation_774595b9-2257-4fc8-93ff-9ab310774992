/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/notifications/email/route";
exports.ids = ["app/api/notifications/email/route"];
exports.modules = {

/***/ "(rsc)/./app/api/notifications/email/route.ts":
/*!**********************************************!*\
  !*** ./app/api/notifications/email/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_email_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/email-service */ \"(rsc)/./lib/email-service.ts\");\n\n\n// Обработчик POST-запроса для отправки email-уведомлений\nasync function POST(request) {\n    try {\n        // Получаем данные из запроса\n        const data = await request.json();\n        // Проверяем наличие необходимых данных\n        if (!data.userEmail || !data.subject || !data.message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Формируем данные для отправки email\n        const reminderData = {\n            userId: data.userId || 'unknown',\n            userEmail: data.userEmail,\n            userName: data.userName || 'Пользователь',\n            subject: data.subject,\n            message: data.message,\n            itemType: data.entityType || 'task',\n            itemId: data.entityId || 'unknown',\n            itemTitle: data.chatName || 'Уведомление',\n            projectName: data.projectName\n        };\n        // Отправляем email\n        const result = await (0,_lib_email_service__WEBPACK_IMPORTED_MODULE_1__.sendReminderEmail)(reminderData);\n        if (result) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: `Email notification sent to ${data.userEmail}`\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'Failed to send email notification'\n            }, {\n                status: 500\n            });\n        }\n    /*\n    // Пример реальной отправки email через nodemailer\n    const transporter = nodemailer.createTransport({\n      host: process.env.SMTP_SERVER,\n      port: parseInt(process.env.SMTP_PORT || '587'),\n      secure: process.env.SMTP_PORT === '465',\n      auth: {\n        user: process.env.SMTP_USERNAME,\n        pass: process.env.SMTP_PASSWORD,\n      },\n    });\n\n    const mailOptions = {\n      from: `\"AI Task Manager\" <${process.env.FROM_EMAIL}>`,\n      to: data.userEmail,\n      subject: data.subject,\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h2 style=\"color: #333;\">${data.subject}</h2>\n          <p>Здравствуйте, ${data.userName || 'пользователь'}!</p>\n          <p>${data.message}</p>\n          <div style=\"background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n            <p><strong>Сообщение:</strong> ${data.messageText}</p>\n            <p><strong>От:</strong> ${data.senderName}</p>\n            <p><strong>Чат:</strong> ${data.chatName}</p>\n          </div>\n          <p>С уважением,<br>Команда AI Task Manager</p>\n        </div>\n      `,\n    };\n\n    const info = await transporter.sendMail(mailOptions);\n\n    return NextResponse.json({\n      success: true,\n      message: `Email notification sent to ${data.userEmail}`,\n      messageId: info.messageId\n    });\n    */ } catch (error) {\n        console.error('Error sending email notification:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'Failed to send email notification',\n            error: String(error)\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/notifications/email/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/email-service.ts":
/*!******************************!*\
  !*** ./lib/email-service.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendReminderEmail: () => (/* binding */ sendReminderEmail)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"40a3470f6f9b2080776491b136ec9b2d0412ffed56\":\"sendReminderEmail\"} */ \n\n\n// Получение настроек SMTP из базы данных или из переменных окружения\nasync function getEmailSettings() {\n    // В реальном приложении здесь будет запрос к базе данных для получения настроек\n    // Для примера используем переменные окружения\n    return {\n        smtpServer: process.env.SMTP_SERVER || '',\n        smtpPort: process.env.SMTP_PORT || '587',\n        smtpUsername: process.env.SMTP_USERNAME || '',\n        smtpPassword: process.env.SMTP_PASSWORD || '',\n        fromEmail: process.env.FROM_EMAIL || '<EMAIL>'\n    };\n}\n// Создание транспорта для отправки писем\nasync function createTransport() {\n    const settings = await getEmailSettings();\n    // Проверяем, что все необходимые настройки указаны\n    if (!settings.smtpServer || !settings.smtpUsername || !settings.smtpPassword) {\n        throw new Error('SMTP settings are not configured');\n    }\n    return nodemailer__WEBPACK_IMPORTED_MODULE_2__.createTransport({\n        host: settings.smtpServer,\n        port: parseInt(settings.smtpPort, 10),\n        secure: parseInt(settings.smtpPort, 10) === 465,\n        auth: {\n            user: settings.smtpUsername,\n            pass: settings.smtpPassword\n        }\n    });\n}\n// Функция для отправки напоминания по электронной почте\nasync function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ sendReminderEmail(reminderData) {\n    try {\n        // Получаем настройки электронной почты\n        const settings = await getEmailSettings();\n        // Проверяем, что все необходимые настройки указаны\n        if (!settings.smtpServer || !settings.smtpUsername || !settings.smtpPassword) {\n            console.error('SMTP settings are not configured');\n            return false;\n        }\n        // Проверяем, что у пользователя есть email\n        if (!reminderData.userEmail) {\n            console.error(`User ${reminderData.userId} does not have an email address`);\n            return false;\n        }\n        // Создаем транспорт\n        const transporter = await createTransport();\n        // Формируем HTML для письма\n        const htmlContent = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n        <h2 style=\"color: #333;\">Напоминание</h2>\n        <p>Здравствуйте, ${reminderData.userName || 'пользователь'}!</p>\n        <p>${reminderData.message}</p>\n        <div style=\"background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;\">\n          <h3 style=\"margin-top: 0; color: #333;\">${reminderData.itemTitle}</h3>\n          ${reminderData.projectName ? `<p><strong>Проект:</strong> ${reminderData.projectName}</p>` : ''}\n          ${reminderData.dueDate ? `<p><strong>Срок:</strong> ${reminderData.dueDate.toLocaleDateString('ru-RU')}</p>` : ''}\n        </div>\n        <p>С уважением,<br>Команда AI Task Tracker</p>\n      </div>\n    `;\n        // Отправляем письмо\n        const info = await transporter.sendMail({\n            from: `\"AI Task Tracker\" <${settings.fromEmail}>`,\n            to: reminderData.userEmail,\n            subject: reminderData.subject,\n            html: htmlContent\n        });\n        console.log(`Email sent to ${reminderData.userEmail}: ${info.messageId}`);\n        // Записываем информацию об отправленном напоминании в лог\n        await logReminderSent(reminderData);\n        return true;\n    } catch (error) {\n        console.error('Error sending reminder email:', error);\n        return false;\n    }\n}\n// Функция для записи информации об отправленном напоминании в лог\nasync function logReminderSent(reminderData) {\n    try {\n        // В реальном приложении здесь будет запись в базу данных\n        console.log(`Reminder sent to ${reminderData.userEmail} for ${reminderData.itemType} \"${reminderData.itemTitle}\"`);\n    // Пример записи в базу данных (закомментировано, так как модель не существует)\n    /*\n    await prisma.reminderLog.create({\n      data: {\n        userId: reminderData.userId,\n        itemType: reminderData.itemType,\n        itemId: reminderData.itemId,\n        sentAt: new Date(),\n        success: true,\n      },\n    });\n    */ } catch (error) {\n        console.error('Error logging reminder:', error);\n    }\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_3__.ensureServerEntryExports)([\n    sendReminderEmail\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(sendReminderEmail, \"40a3470f6f9b2080776491b136ec9b2d0412ffed56\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/email-service.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Femail%2Froute&page=%2Fapi%2Fnotifications%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Femail%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Femail%2Froute&page=%2Fapi%2Fnotifications%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Femail%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Projects_task_ai_manager3_app_api_notifications_email_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/notifications/email/route.ts */ \"(rsc)/./app/api/notifications/email/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/notifications/email/route\",\n        pathname: \"/api/notifications/email\",\n        filename: \"route\",\n        bundlePath: \"app/api/notifications/email/route\"\n    },\n    resolvedPagePath: \"D:\\\\Projects\\\\task-ai-manager3\\\\app\\\\api\\\\notifications\\\\email\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Projects_task_ai_manager3_app_api_notifications_email_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZub3RpZmljYXRpb25zJTJGZW1haWwlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRm5vdGlmaWNhdGlvbnMlMkZlbWFpbCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRm5vdGlmaWNhdGlvbnMlMkZlbWFpbCUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDUHJvamVjdHMlNUN0YXNrLWFpLW1hbmFnZXIzJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDUHJvamVjdHMlNUN0YXNrLWFpLW1hbmFnZXIzJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUN3QjtBQUNyRztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxhcHBcXFxcYXBpXFxcXG5vdGlmaWNhdGlvbnNcXFxcZW1haWxcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL25vdGlmaWNhdGlvbnMvZW1haWwvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9ub3RpZmljYXRpb25zL2VtYWlsXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9ub3RpZmljYXRpb25zL2VtYWlsL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxhcHBcXFxcYXBpXFxcXG5vdGlmaWNhdGlvbnNcXFxcZW1haWxcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Femail%2Froute&page=%2Fapi%2Fnotifications%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Femail%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Cemail-service.ts%22%2C%5B%7B%22id%22%3A%2240a3470f6f9b2080776491b136ec9b2d0412ffed56%22%2C%22exportedName%22%3A%22sendReminderEmail%22%7D%5D%5D%5D&__client_imported__=!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Cemail-service.ts%22%2C%5B%7B%22id%22%3A%2240a3470f6f9b2080776491b136ec9b2d0412ffed56%22%2C%22exportedName%22%3A%22sendReminderEmail%22%7D%5D%5D%5D&__client_imported__=! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"40a3470f6f9b2080776491b136ec9b2d0412ffed56\": () => (/* reexport safe */ D_Projects_task_ai_manager3_lib_email_service_ts__WEBPACK_IMPORTED_MODULE_0__.sendReminderEmail)\n/* harmony export */ });\n/* harmony import */ var D_Projects_task_ai_manager3_lib_email_service_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/email-service.ts */ \"(rsc)/./lib/email-service.ts\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1hY3Rpb24tZW50cnktbG9hZGVyLmpzP2FjdGlvbnM9JTVCJTVCJTIyRCUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDdGFzay1haS1tYW5hZ2VyMyU1QyU1Q2xpYiU1QyU1Q2VtYWlsLXNlcnZpY2UudHMlMjIlMkMlNUIlN0IlMjJpZCUyMiUzQSUyMjQwYTM0NzBmNmY5YjIwODA3NzY0OTFiMTM2ZWM5YjJkMDQxMmZmZWQ1NiUyMiUyQyUyMmV4cG9ydGVkTmFtZSUyMiUzQSUyMnNlbmRSZW1pbmRlckVtYWlsJTIyJTdEJTVEJTVEJTVEJl9fY2xpZW50X2ltcG9ydGVkX189ISIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFDeUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IHNlbmRSZW1pbmRlckVtYWlsIGFzIFwiNDBhMzQ3MGY2ZjliMjA4MDc3NjQ5MWIxMzZlYzliMmQwNDEyZmZlZDU2XCIgfSBmcm9tIFwiRDpcXFxcUHJvamVjdHNcXFxcdGFzay1haS1tYW5hZ2VyM1xcXFxsaWJcXFxcZW1haWwtc2VydmljZS50c1wiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CProjects%5C%5Ctask-ai-manager3%5C%5Clib%5C%5Cemail-service.ts%22%2C%5B%7B%22id%22%3A%2240a3470f6f9b2080776491b136ec9b2d0412ffed56%22%2C%22exportedName%22%3A%22sendReminderEmail%22%7D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/nodemailer"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fnotifications%2Femail%2Froute&page=%2Fapi%2Fnotifications%2Femail%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnotifications%2Femail%2Froute.ts&appDir=D%3A%5CProjects%5Ctask-ai-manager3%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5Ctask-ai-manager3&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();