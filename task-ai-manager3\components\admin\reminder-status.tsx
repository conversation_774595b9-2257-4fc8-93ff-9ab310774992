"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { 
  Bell, 
  BellRing, 
  Clock, 
  Mail, 
  MessageSquare, 
  Play, 
  Pause, 
  RefreshCw,
  Settings,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { 
  getReminderSchedulerStats,
  isReminderSchedulerRunning,
  startReminderScheduler,
  stopReminderScheduler,
  manualReminderCheck,
  getSavedReminderCheckInterval,
  saveReminderCheckInterval,
  setReminderCheckInterval
} from '@/lib/reminder-scheduler';

export function ReminderStatus() {
  const { toast } = useToast();
  const [isRunning, setIsRunning] = useState(false);
  const [intervalMinutes, setIntervalMinutes] = useState(5);
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheckTime, setLastCheckTime] = useState<Date | null>(null);
  const [stats, setStats] = useState({
    emailReminders: false,
    telegramReminders: false,
    totalUsers: 0,
    activeReminders: 0,
  });

  // Загрузка начального состояния
  useEffect(() => {
    updateStatus();
    loadUserStats();
    
    // Обновляем статус каждые 30 секунд
    const interval = setInterval(updateStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // Функция для обновления статуса планировщика
  const updateStatus = () => {
    setIsRunning(isReminderSchedulerRunning());
    setIntervalMinutes(getSavedReminderCheckInterval());
  };

  // Функция для загрузки статистики пользователей
  const loadUserStats = () => {
    if (typeof window === 'undefined') return;
    
    const emailReminders = localStorage.getItem('emailReminders') === 'true';
    const telegramReminders = localStorage.getItem('telegramReminders') === 'true';
    
    setStats({
      emailReminders,
      telegramReminders,
      totalUsers: 1, // В реальном приложении это будет из API
      activeReminders: emailReminders || telegramReminders ? 1 : 0,
    });
  };

  // Функция для запуска планировщика
  const handleStartScheduler = () => {
    startReminderScheduler(intervalMinutes);
    setIsRunning(true);
    toast({
      title: "Планировщик запущен",
      description: `Напоминания будут проверяться каждые ${intervalMinutes} минут`,
    });
  };

  // Функция для остановки планировщика
  const handleStopScheduler = () => {
    stopReminderScheduler();
    setIsRunning(false);
    toast({
      title: "Планировщик остановлен",
      description: "Автоматическая проверка напоминаний отключена",
    });
  };

  // Функция для ручной проверки напоминаний
  const handleManualCheck = async () => {
    setIsChecking(true);
    try {
      await manualReminderCheck();
      setLastCheckTime(new Date());
      toast({
        title: "Проверка завершена",
        description: "Напоминания проверены и отправлены",
      });
    } catch (error) {
      toast({
        title: "Ошибка",
        description: "Не удалось выполнить проверку напоминаний",
        variant: "destructive",
      });
    } finally {
      setIsChecking(false);
    }
  };

  // Функция для изменения интервала проверки
  const handleIntervalChange = (newInterval: number) => {
    setIntervalMinutes(newInterval);
    saveReminderCheckInterval(newInterval);
    setReminderCheckInterval(newInterval);
    
    if (isRunning) {
      startReminderScheduler(newInterval);
    }
    
    toast({
      title: "Интервал обновлен",
      description: `Интервал проверки изменен на ${newInterval} минут`,
    });
  };

  // Функция для тестирования API напоминаний
  const handleTestAPI = async () => {
    try {
      const response = await fetch('/api/reminders?action=check-all');
      const data = await response.json();
      
      if (data.success) {
        toast({
          title: "API работает",
          description: "Тестовый запрос к API напоминаний выполнен успешно",
        });
      } else {
        toast({
          title: "Ошибка API",
          description: data.message || "Неизвестная ошибка",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Ошибка соединения",
        description: "Не удалось подключиться к API напоминаний",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Статус системы напоминаний
          </CardTitle>
          <CardDescription>
            Мониторинг и управление автоматическими напоминаниями
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Статус планировщика */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              {isRunning ? (
                <BellRing className="h-5 w-5 text-green-500" />
              ) : (
                <Bell className="h-5 w-5 text-gray-400" />
              )}
              <div>
                <p className="font-medium">Планировщик напоминаний</p>
                <p className="text-sm text-muted-foreground">
                  {isRunning 
                    ? `Активен (проверка каждые ${intervalMinutes} мин)` 
                    : 'Остановлен'
                  }
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              {isRunning ? (
                <Button variant="outline" size="sm" onClick={handleStopScheduler}>
                  <Pause className="mr-2 h-4 w-4" />
                  Остановить
                </Button>
              ) : (
                <Button size="sm" onClick={handleStartScheduler}>
                  <Play className="mr-2 h-4 w-4" />
                  Запустить
                </Button>
              )}
            </div>
          </div>

          {/* Настройки интервала */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-blue-500" />
              <div>
                <p className="font-medium">Интервал проверки</p>
                <p className="text-sm text-muted-foreground">
                  Как часто проверять напоминания
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              {[1, 5, 15, 30, 60].map((interval) => (
                <Button
                  key={interval}
                  variant={intervalMinutes === interval ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleIntervalChange(interval)}
                >
                  {interval}м
                </Button>
              ))}
            </div>
          </div>

          {/* Статистика */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 border rounded-lg text-center">
              <Mail className="h-6 w-6 mx-auto mb-2 text-blue-500" />
              <p className="text-sm text-muted-foreground">Email</p>
              <Badge variant={stats.emailReminders ? "default" : "secondary"}>
                {stats.emailReminders ? "Включен" : "Отключен"}
              </Badge>
            </div>
            
            <div className="p-4 border rounded-lg text-center">
              <MessageSquare className="h-6 w-6 mx-auto mb-2 text-green-500" />
              <p className="text-sm text-muted-foreground">Telegram</p>
              <Badge variant={stats.telegramReminders ? "default" : "secondary"}>
                {stats.telegramReminders ? "Включен" : "Отключен"}
              </Badge>
            </div>
            
            <div className="p-4 border rounded-lg text-center">
              <CheckCircle className="h-6 w-6 mx-auto mb-2 text-purple-500" />
              <p className="text-sm text-muted-foreground">Активные</p>
              <p className="text-lg font-semibold">{stats.activeReminders}</p>
            </div>
            
            <div className="p-4 border rounded-lg text-center">
              <AlertCircle className="h-6 w-6 mx-auto mb-2 text-orange-500" />
              <p className="text-sm text-muted-foreground">Пользователи</p>
              <p className="text-lg font-semibold">{stats.totalUsers}</p>
            </div>
          </div>

          {/* Действия */}
          <div className="flex gap-2 pt-4">
            <Button 
              variant="outline" 
              onClick={handleManualCheck}
              disabled={isChecking}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isChecking ? 'animate-spin' : ''}`} />
              {isChecking ? 'Проверка...' : 'Проверить сейчас'}
            </Button>
            
            <Button variant="outline" onClick={handleTestAPI}>
              <Settings className="mr-2 h-4 w-4" />
              Тест API
            </Button>
            
            <Button variant="outline" onClick={loadUserStats}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Обновить статистику
            </Button>
          </div>

          {/* Последняя проверка */}
          {lastCheckTime && (
            <div className="text-sm text-muted-foreground pt-2 border-t">
              Последняя проверка: {lastCheckTime.toLocaleString('ru-RU')}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
